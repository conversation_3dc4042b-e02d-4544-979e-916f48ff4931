module oop2.project {
    requires javafx.controls;
    requires javafx.fxml;
    requires javafx.web;

    requires org.controlsfx.controls;
    requires com.dlsc.formsfx;
    requires net.synedra.validatorfx;
    requires org.kordamp.ikonli.javafx;
    requires org.kordamp.ikonli.fontawesome5;
    requires org.kordamp.bootstrapfx.core;
    requires org.tinylog.api;
    requires org.tinylog.impl;

    // Basic Java modules
    requires java.net.http;

    // Automatic modules
    requires com.fasterxml.jackson.databind;
    requires com.fasterxml.jackson.annotation;
    requires com.fasterxml.jackson.core;
    requires annotations;
    requires info.movito.themoviedbapi;

    opens oop2.project to javafx.fxml;
    exports oop2.project;

//    // Configuration
//    exports oop2.project.config;
//    opens oop2.project.config to javafx.fxml;
//
    // Controllers
    exports oop2.project.controller;
    opens oop2.project.controller to javafx.fxml;
//
//    // Models for JSON serialization/deserialization
//    exports oop2.project.model;
//    opens oop2.project.model to javafx.fxml, com.fasterxml.jackson.databind;
//
//    // API services
//    exports oop2.project.api;
//    opens oop2.project.api to javafx.fxml;
}
