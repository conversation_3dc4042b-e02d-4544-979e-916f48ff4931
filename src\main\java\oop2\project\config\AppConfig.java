package oop2.project.config;

import oop2.project.model.Language;
import org.tinylog.Logger;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.*;

public class AppConfig {
    public static final Path CONFIG_FILE_PATH = Path.of(System.getProperty("user.home"), "oop2AppDiegoConfig", "appConfig.properties");

    public static final String DEFAULT_LANGUAGE = "en-US";
    public static final boolean DEFAULT_ALLOW_ADULT_CONTENT = false;
    public static final double DEFAULT_MINIMUM_RATING = -1.0f;

    public static final String KEY_LANGUAGE = "language";
    public static final String KEY_ALLOW_ADULT_CONTENT = "allowAdultContent";
    public static final String KEY_MINIMUM_RATING = "minimumRating";
    public static final String KEY_OPENAI_API_KEY = "openAiApiKey";
    public static final String KEY_TMDB_API_KEY = "tmdbApiKey";

    private final Map<String, String> properties = new HashMap<>() {{
        put(KEY_LANGUAGE, DEFAULT_LANGUAGE);
        put(KEY_ALLOW_ADULT_CONTENT, String.valueOf(DEFAULT_ALLOW_ADULT_CONTENT));
        put(KEY_MINIMUM_RATING, String.valueOf(DEFAULT_MINIMUM_RATING));
        put(KEY_OPENAI_API_KEY, "");
        put(KEY_TMDB_API_KEY, "");
    }};

    private boolean isDirty;

    private static final AppConfig instance = new AppConfig();

    private AppConfig() {
        load();
    }

    public static AppConfig getInstance() {
        return instance;
    }

    // Load configuration from an InputStream
    private void load() {
        try (InputStream inputStream = Files.newInputStream(CONFIG_FILE_PATH);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            String line;

            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.startsWith("#") || line.isEmpty()) {
                    continue;
                }
                String[] parts = line.split("=", 2);
                if (parts.length == 2) {
                    // Overwrite default properties with values from the stream
                    properties.put(parts[0].trim(), parts[1].trim());
                } else {
                    Logger.warn("Skipping malformed line in config stream: {}", line);
                }
            }

            Logger.info("Configuration loaded successfully from stream.");
        } catch (IOException e) {
            loadAndSaveDefaults();
            Logger.warn("Failed to load config from input stream. Resetting to defaults.");
        }
    }

    private void loadAndSaveDefaults() {
        isDirty = true;
        ensurePathToConfigExists();
        save();
    }

    public void save() {
        if(!isDirty) {
            return;
        }

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(CONFIG_FILE_PATH), StandardCharsets.UTF_8))) {
            // Add comments for clarity
            writer.write("# Application Configuration");
            writer.newLine();
            writer.newLine();

            // Write properties sorted by key for consistency
            List<String> sortedKeys = new ArrayList<>(properties.keySet());
            Collections.sort(sortedKeys);

            for (String key : sortedKeys) {
                writer.write(key + "=" + properties.get(key));
                writer.newLine();
            }
            writer.flush(); // Ensure data is written to the underlying stream

            isDirty = false; // Saved successfully
        } catch (IOException e) {
            Logger.error(e, "Failed to save config to file.");
        }
    }

    private void ensurePathToConfigExists() {
        File configDir = CONFIG_FILE_PATH.getParent().toFile();

        if (!configDir.exists()) {
            if (!configDir.mkdirs()) {
                Logger.error("Failed to create config directory: {}", configDir.getAbsolutePath());
            }
        }
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        String value = properties.get(key);
        if (value == null) {
            return null;
        }

        try {
            if (type == String.class) {
                return (T) value;
            } else if (type == Boolean.class) {
                return (T) Boolean.valueOf(value);
            } else if (type == Integer.class) {
                return (T) Integer.valueOf(value);
            } else if (type == Float.class) {
                return (T) Float.valueOf(value);
            } else if (type == Double.class) {
                return (T) Double.valueOf(value);
            } else {
                Logger.warn("Unsupported type requested from config: {}", type.getName());
                return null;
            }
        } catch (Exception e) {
            Logger.warn(e, "Failed to parse config value for key '{}' as type '{}'", key, type.getSimpleName());
            return null;
        }
    }

    public String get(String key) {
        return properties.get(key);
    }

    public void set(String key, String value) {
        properties.put(key, value);
        isDirty = true;
    }

    public boolean isValid(){
        var openAIKeyExists = !get(KEY_OPENAI_API_KEY).isBlank();
        var tmdbKeyExists = !get(KEY_TMDB_API_KEY).isBlank();

        return openAIKeyExists && tmdbKeyExists;
    }

    public Language getLanguage() {
        return Language.valueOf(get(KEY_LANGUAGE));
    }

    public void setLanguage(Language language) {
        properties.put(KEY_LANGUAGE, language.name());
        isDirty = true;
    }

    public double getMinimumRating() {
        return get(properties.get(KEY_MINIMUM_RATING), Double.class);
    }

    public void setMinimumRating(double minimumRating) {
        properties.put(KEY_MINIMUM_RATING, String.valueOf(minimumRating));
        isDirty = true;
    }

    public String getOpenAiApiKey() {
        return get(KEY_OPENAI_API_KEY);
    }

    public void setOpenAiApiKey(String openAiApiKey) {
        properties.put(KEY_OPENAI_API_KEY, openAiApiKey);
        isDirty = true;
    }

    public String getTmdbApiKey() {
        return get(KEY_TMDB_API_KEY);
    }

    public void setTmdbApiKey(String tmdbApiKey) {
        properties.put(KEY_TMDB_API_KEY, tmdbApiKey);
        isDirty = true;
    }

    public boolean allowAdultContent() {
        return get(KEY_ALLOW_ADULT_CONTENT, Boolean.class);
    }

    public void setAllowAdultContent(boolean allow) {
        properties.put(KEY_ALLOW_ADULT_CONTENT,  String.valueOf(allow));
        isDirty = true;
    }
}
