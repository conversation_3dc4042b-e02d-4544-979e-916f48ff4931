package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.VBox;
import info.movito.themoviedbapi.model.core.Movie;
import org.tinylog.Logger;

import java.util.List;
import java.util.Optional;

/**
 * Controller for individual movie card components.
 * Handles the presentation logic for displaying movie information in card format.
 */
public class MovieCardController {

    @FXML
    private ImageView moviePosterImage;
    
    @FXML
    private VBox posterPlaceholder;
    
    @FXML
    private Label ratingLabel;
    
    @FXML
    private Label movieTitleLabel;
    
    @FXML
    private Label movieYearLabel;
    
    @FXML
    private Label movieDurationLabel;
    
    @FXML
    private FlowPane genreContainer;
    
    @FXML
    private Label movieOverviewLabel;

    private Movie movie;

    /**
     * Initializes the movie card with the provided movie data.
     * 
     * @param movie the movie to display
     */
    public void setMovie(Movie movie) {
        if (movie == null) {
            Logger.warn("Attempted to set null movie in MovieCardController");
            return;
        }
        
        this.movie = movie;
        updateMovieDisplay();
    }

    /**
     * Updates all UI elements with the current movie data.
     */
    private void updateMovieDisplay() {
        updatePoster();
        updateBasicInfo();
        updateGenres();
        updateOverview();
    }

    /**
     * Updates the movie poster image or shows placeholder.
     */
    private void updatePoster() {
        String posterPath = movie.getPosterPath();
        String posterUrl = null;

        if (posterPath != null && !posterPath.isEmpty()) {
            posterUrl = "https://image.tmdb.org/t/p/w500" + posterPath;
        }

        if (posterUrl != null && !posterUrl.isEmpty()) {
            try {
                Image posterImage = new Image(posterUrl, true); // Load in background
                moviePosterImage.setImage(posterImage);
                moviePosterImage.setVisible(true);
                posterPlaceholder.setVisible(false);
            } catch (Exception e) {
                Logger.debug("Failed to load poster image for movie: {}", movie.getTitle());
                showPosterPlaceholder();
            }
        } else {
            showPosterPlaceholder();
        }
    }

    /**
     * Shows the poster placeholder when image is not available.
     */
    private void showPosterPlaceholder() {
        moviePosterImage.setVisible(false);
        posterPlaceholder.setVisible(true);
    }

    /**
     * Updates basic movie information (title, year, rating, duration).
     */
    private void updateBasicInfo() {
        movieTitleLabel.setText(Optional.ofNullable(movie.getTitle()).orElse("Unknown Title"));

        Integer year = extractReleaseYear();
        movieYearLabel.setText(year != null ? year.toString() : "Unknown");

        ratingLabel.setText(String.format("%.1f", movie.getVoteAverage()));

        // Duration is not always available from discover endpoint
        // The external Movie class might not have runtime for discover results
        movieDurationLabel.setVisible(false);
    }

    /**
     * Extracts the release year from the release date string.
     *
     * @return the release year or null if not available
     */
    private Integer extractReleaseYear() {
        String releaseDate = movie.getReleaseDate();
        if (releaseDate == null || releaseDate.isEmpty()) {
            return null;
        }

        try {
            // Release date format is typically "YYYY-MM-DD"
            return Integer.parseInt(releaseDate.substring(0, 4));
        } catch (Exception e) {
            Logger.debug("Failed to parse release year from: {}", releaseDate);
            return null;
        }
    }

    /**
     * Updates the genre display using streams for data processing.
     */
    private void updateGenres() {
        genreContainer.getChildren().clear();

        // The external Movie class uses getGenreIds() method
        List<Integer> genreIds = movie.getGenreIds();
        if (genreIds != null && !genreIds.isEmpty()) {
            genreIds.stream()
                    .limit(3) // Limit to 3 genres for space
                    .map(this::createGenreLabel)
                    .forEach(genreContainer.getChildren()::add);
        }
    }

    /**
     * Creates a genre label for the given genre ID.
     * 
     * @param genreId the genre ID
     * @return a Label with appropriate styling
     */
    private Label createGenreLabel(Integer genreId) {
        Label genreLabel = new Label(getGenreName(genreId));
        genreLabel.getStyleClass().addAll("genre-tag", getGenreStyleClass(genreId));
        return genreLabel;
    }

    /**
     * Updates the movie overview with proper text truncation.
     */
    private void updateOverview() {
        String overview = movie.getOverview();
        if (overview != null && !overview.isEmpty()) {
            // Truncate overview for card display
            String truncatedOverview = overview.length() > 150 
                    ? overview.substring(0, 147) + "..." 
                    : overview;
            movieOverviewLabel.setText(truncatedOverview);
            movieOverviewLabel.setVisible(true);
        } else {
            movieOverviewLabel.setVisible(false);
        }
    }

    /**
     * Maps genre ID to human-readable name.
     * 
     * @param genreId the genre ID
     * @return the genre name
     */
    private String getGenreName(Integer genreId) {
        // Common TMDB genre IDs - could be moved to a constants class
        return switch (genreId) {
            case 28 -> "Action";
            case 12 -> "Adventure";
            case 16 -> "Animation";
            case 35 -> "Comedy";
            case 80 -> "Crime";
            case 99 -> "Documentary";
            case 18 -> "Drama";
            case 10751 -> "Family";
            case 14 -> "Fantasy";
            case 36 -> "History";
            case 27 -> "Horror";
            case 10402 -> "Music";
            case 9648 -> "Mystery";
            case 10749 -> "Romance";
            case 878 -> "Sci-Fi";
            case 10770 -> "TV Movie";
            case 53 -> "Thriller";
            case 10752 -> "War";
            case 37 -> "Western";
            default -> "Other";
        };
    }

    /**
     * Maps genre ID to CSS style class for color coding.
     * 
     * @param genreId the genre ID
     * @return the CSS style class
     */
    private String getGenreStyleClass(Integer genreId) {
        return switch (genreId) {
            case 28 -> "action";
            case 35 -> "comedy";
            case 18 -> "drama";
            case 53 -> "thriller";
            case 27 -> "horror";
            case 10749 -> "romance";
            case 878 -> "sci-fi";
            default -> "";
        };
    }

    /**
     * Gets the current movie.
     * 
     * @return the current movie
     */
    public Movie getMovie() {
        return movie;
    }
}
