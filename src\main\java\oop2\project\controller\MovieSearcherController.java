package oop2.project.controller;

import javafx.application.Platform;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import oop2.project.Main;
import oop2.project.config.AppConfig;
import oop2.project.model.Language;
import info.movito.themoviedbapi.model.core.Movie;
import oop2.project.model.MovieSearchResult;
import oop2.project.model.TmdbQuery;
import oop2.project.service.TmdbService;
import oop2.project.util.RatingSliderUtil;
import org.tinylog.Logger;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * Controller for the movie searcher view.
 * Handles presentation logic for movie discovery and display.
 */
public class MovieSearcherController {

    // FXML UI Components
    @FXML
    private TextField searchField;

    @FXML
    private ComboBox<Language> languageFilterCombo;

    @FXML
    private Slider minRatingSlider;

    @FXML
    private Label ratingLabel;

    @FXML
    private CheckBox adultContentFilterCheckbox;

    @FXML
    private FlowPane movieGridContainer;

    @FXML
    private VBox movieListContainer;

    @FXML
    private StackPane contentArea;

    @FXML
    private VBox loadingIndicator;

    @FXML
    private VBox emptyStateContainer;

    @FXML
    private VBox errorStateContainer;

    @FXML
    private Label errorMessageLabel;

    @FXML
    private Button retryButton;



    // Pagination controls
    @FXML
    private Button firstPageButton;

    @FXML
    private Button prevPageButton;

    @FXML
    private Button nextPageButton;

    @FXML
    private Button lastPageButton;

    @FXML
    private ComboBox<Integer> itemsPerPageCombo;

    @FXML
    private HBox pageNumberContainer;

    // Domain service (injected, not static)
    private final TmdbService tmdbService;
    private final AppConfig config;

    // State management
    private MovieSearchResult currentSearchResult;
    private int currentPage = 1;
    private CompletableFuture<MovieSearchResult> currentSearchTask;

    /**
     * Constructor with dependency injection.
     */
    public MovieSearcherController() {
        this.tmdbService = new TmdbService();
        this.config = AppConfig.getInstance();
    }

    @FXML
    public void initialize() {
        setupUIComponents();
        setupEventHandlers();
        performInitialSearch();
    }

    /**
     * Sets up initial UI component states.
     */
    private void setupUIComponents() {
        // Setup language combo box
        languageFilterCombo.getItems().addAll(Language.values());
        languageFilterCombo.setValue(config.getLanguage());

        // Setup rating slider
        RatingSliderUtil.setupRatingSlider(minRatingSlider, ratingLabel);

        // Setup adult content checkbox
        adultContentFilterCheckbox.setSelected(config.allowAdultContent());

        // Setup items per page combo (TMDB API returns 20 items per page by default)
        itemsPerPageCombo.getItems().addAll(20);
        itemsPerPageCombo.setValue(20);
        itemsPerPageCombo.setDisable(true); // Disabled since TMDB API doesn't support custom page sizes

        // Initially show loading state
        showLoadingState();
    }

    /**
     * Sets up event handlers for UI interactions.
     */
    private void setupEventHandlers() {
        // Search on Enter key in search field
        searchField.setOnAction(e -> performSearch());



        // Retry button
        retryButton.setOnAction(e -> performSearch());

        // Pagination buttons
        firstPageButton.setOnAction(e -> navigateToPage(1));
        prevPageButton.setOnAction(e -> navigateToPage(currentPage - 1));
        nextPageButton.setOnAction(e -> navigateToPage(currentPage + 1));
        lastPageButton.setOnAction(e -> {
            if (currentSearchResult != null) {
                navigateToPage(currentSearchResult.getTotalPages());
            }
        });

        // Auto-search on filter changes (with debouncing could be added)
        languageFilterCombo.setOnAction(e -> performSearch());
        minRatingSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (!oldVal.equals(newVal)) {
                performSearch();
            }
        });
        adultContentFilterCheckbox.setOnAction(e -> performSearch());
    }

    /**
     * Performs initial search when the view loads.
     */
    private void performInitialSearch() {
        performSearch();
    }

    /**
     * Performs a movie search based on current UI filter values.
     */
    private void performSearch() {
        performSearchForPage(1);
    }

    /**
     * Performs a movie search for a specific page.
     *
     * @param page the page number to search
     */
    private void performSearchForPage(int page) {
        // Cancel any ongoing search
        if (currentSearchTask != null && !currentSearchTask.isDone()) {
            currentSearchTask.cancel(true);
        }

        currentPage = page;
        showLoadingState();

        // Build query from UI inputs and set the page number
        TmdbQuery query = buildQueryFromUI();
        query.setPage(page);

        // Perform async search
        currentSearchTask = tmdbService.discoverMovies(query)
                .thenApply(result -> {
                    Platform.runLater(() -> handleSearchSuccess(result));
                    return result;
                })
                .exceptionally(throwable -> {
                    Platform.runLater(() -> handleSearchError(throwable));
                    return null;
                });
    }

    /**
     * Builds a TmdbQuery from current UI filter values.
     *
     * @return configured TmdbQuery
     */
    private TmdbQuery buildQueryFromUI() {
        TmdbQuery query = new TmdbQuery();

        // Set language from combo box
        Language selectedLanguage = languageFilterCombo.getValue();
        if (selectedLanguage != null) {
            query.setLanguage(selectedLanguage);
        }

        // Set minimum rating from slider
        double minRating = RatingSliderUtil.getSliderValue(minRatingSlider);
        if (minRating > 0) {
            query.setMinimumRating(minRating);
        }

        // Set adult content filter
        query.includeAdultContent(adultContentFilterCheckbox.isSelected());

        // Note: Search text is not used in discover endpoint
        // For text search, a different endpoint would be needed

        return query;
    }

    /**
     * Handles successful search results.
     *
     * @param result the search result
     */
    private void handleSearchSuccess(MovieSearchResult result) {
        currentSearchResult = result;

        if (result.getResults().isEmpty()) {
            showEmptyState();
        } else {
            displayMovies(result.getResults());
            updatePaginationControls();
            showContentState();
        }
    }

    /**
     * Handles search errors with proper exception handling.
     *
     * @param throwable the error that occurred
     */
    private void handleSearchError(Throwable throwable) {
        Logger.error(throwable, "Error performing movie search");

        String errorMessage = "Unable to load movies. Please check your internet connection and try again.";
        if (throwable.getCause() != null) {
            String cause = throwable.getCause().getCause().getMessage();
            if (cause != null && cause.contains("401")) {
                errorMessage = "Invalid API key. Please check your TMDB API key in settings.";
            } else if (cause != null && cause.contains("timeout")) {
                errorMessage = "Request timed out. Please try again.";
            }
        }

        errorMessageLabel.setText(errorMessage);
        showErrorState();
    }

    /**
     * Displays movies in the grid container using streams for processing.
     *
     * @param movies the list of movies to display
     */
    private void displayMovies(List<Movie> movies) {
        movieGridContainer.getChildren().clear();

        // Use streams for declarative data processing
        movies.stream()
                .filter(movie -> movie != null) // Filter out null movies
                .map(this::createMovieCard)
                .filter(node -> node != null) // Filter out failed card creations
                .forEach(movieGridContainer.getChildren()::add);
    }

    /**
     * Creates a movie card node for the given movie.
     *
     * @param movie the movie to create a card for
     * @return the movie card node or null if creation fails
     */
    private Node createMovieCard(Movie movie) {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/MovieCard.fxml"));
            Node movieCard = loader.load();

            // Get controller and set movie data
            MovieCardController controller = loader.getController();
            if (controller != null) {
                controller.setMovie(movie);
            }

            return movieCard;
        } catch (IOException e) {
            Logger.error(e, "Failed to create movie card for movie: {}", movie.getTitle());
            return null;
        }
    }

    /**
     * Navigates to a specific page.
     *
     * @param page the page number to navigate to
     */
    private void navigateToPage(int page) {
        if (currentSearchResult == null) {
            return;
        }

        if (page < 1 || page > currentSearchResult.getTotalPages()) {
            return;
        }

        performSearchForPage(page);
    }

    /**
     * Updates pagination control states based on current search result.
     */
    private void updatePaginationControls() {
        if (currentSearchResult == null) {
            return;
        }

        boolean hasPrevious = currentPage > 1;
        boolean hasNext = currentPage < currentSearchResult.getTotalPages();

        firstPageButton.setDisable(!hasPrevious);
        prevPageButton.setDisable(!hasPrevious);
        nextPageButton.setDisable(!hasNext);
        lastPageButton.setDisable(!hasNext);

        // Update page number display
        updatePageNumberDisplay();
    }

    /**
     * Updates the page number display with clickable page buttons.
     */
    private void updatePageNumberDisplay() {
        if (currentSearchResult == null || pageNumberContainer == null) {
            return;
        }

        pageNumberContainer.getChildren().clear();

        int totalPages = currentSearchResult.getTotalPages();
        int currentPageNum = currentPage;

        // Calculate which pages to show (show max 7 page buttons)
        int startPage = Math.max(1, currentPageNum - 3);
        int endPage = Math.min(totalPages, startPage + 6);

        // Adjust start page if we're near the end
        if (endPage - startPage < 6) {
            startPage = Math.max(1, endPage - 6);
        }

        // Add page buttons using streams for clean code
        IntStream.rangeClosed(startPage, endPage)
                .mapToObj(this::createPageButton)
                .forEach(pageNumberContainer.getChildren()::add);

        // Add page info label
        Label pageInfoLabel = new Label(String.format("Page %d of %d", currentPageNum, totalPages));
        pageInfoLabel.getStyleClass().add("page-info-label");
        pageNumberContainer.getChildren().add(pageInfoLabel);
    }

    /**
     * Creates a page button for the given page number.
     *
     * @param pageNum the page number
     * @return the page button
     */
    private Button createPageButton(int pageNum) {
        Button pageButton = new Button(String.valueOf(pageNum));
        pageButton.getStyleClass().add("page-number-btn");

        if (pageNum == currentPage) {
            pageButton.getStyleClass().add("current-page");
            pageButton.setDisable(true);
        } else {
            pageButton.setOnAction(e -> navigateToPage(pageNum));
        }

        return pageButton;
    }

    // State management methods

    /**
     * Shows the loading state.
     */
    private void showLoadingState() {
        setAllStatesVisible(false);
        if (loadingIndicator != null) {
            loadingIndicator.setVisible(true);
        }
    }

    /**
     * Shows the content state (movies displayed).
     */
    private void showContentState() {
        setAllStatesVisible(false);
        movieGridContainer.setVisible(true);
    }

    /**
     * Shows the empty state (no movies found).
     */
    private void showEmptyState() {
        setAllStatesVisible(false);
        emptyStateContainer.setVisible(true);
    }

    /**
     * Shows the error state.
     */
    private void showErrorState() {
        setAllStatesVisible(false);
        errorStateContainer.setVisible(true);
    }

    /**
     * Sets visibility of all state containers.
     *
     * @param visible the visibility state
     */
    private void setAllStatesVisible(boolean visible) {
        Stream.of(loadingIndicator, emptyStateContainer, errorStateContainer, movieGridContainer)
                .filter(container -> container != null)
                .forEach(container -> container.setVisible(visible));
    }

    /**
     * Resets the search form to default values.
     */
    @FXML
    private void resetSearch() {
        searchField.clear();
        languageFilterCombo.setValue(config.getLanguage());
        minRatingSlider.setValue(-1);
        adultContentFilterCheckbox.setSelected(config.allowAdultContent());
        performSearch();
    }
}
