package oop2.project.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import info.movito.themoviedbapi.model.core.Movie;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents the result of a movie search from TMDB API.
 * This class is designed to be deserialized from JSON responses.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MovieSearchResult {
    
    private int page;
    
    private List<Movie> results = new ArrayList<>();
    
    @JsonProperty("total_pages")
    private int totalPages;
    
    @JsonProperty("total_results")
    private int totalResults;

    /**
     * Default constructor required for Jackson deserialization.
     */
    public MovieSearchResult() {
    }

    /**
     * Gets the current page number.
     * 
     * @return the page number
     */
    public int getPage() {
        return page;
    }

    /**
     * Sets the current page number.
     * 
     * @param page the page number
     */
    public void setPage(int page) {
        this.page = page;
    }

    /**
     * Gets the list of movies in the search result.
     * 
     * @return the list of movies
     */
    public List<Movie> getResults() {
        return results;
    }

    /**
     * Sets the list of movies in the search result.
     * 
     * @param results the list of movies
     */
    public void setResults(List<Movie> results) {
        this.results = results;
    }

    /**
     * Gets the total number of pages.
     * 
     * @return the total number of pages
     */
    public int getTotalPages() {
        return totalPages;
    }

    /**
     * Sets the total number of pages.
     * 
     * @param totalPages the total number of pages
     */
    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    /**
     * Gets the total number of results.
     * 
     * @return the total number of results
     */
    public int getTotalResults() {
        return totalResults;
    }

    /**
     * Sets the total number of results.
     * 
     * @param totalResults the total number of results
     */
    public void setTotalResults(int totalResults) {
        this.totalResults = totalResults;
    }
    
    /**
     * Checks if there are more pages available.
     * 
     * @return true if there are more pages, false otherwise
     */
    public boolean hasMorePages() {
        return page < totalPages;
    }
    
    /**
     * Checks if this is the first page.
     * 
     * @return true if this is the first page, false otherwise
     */
    public boolean isFirstPage() {
        return page == 1;
    }
    
    /**
     * Gets the next page number.
     * 
     * @return the next page number or -1 if there are no more pages
     */
    public int getNextPage() {
        return hasMorePages() ? page + 1 : -1;
    }
    
    /**
     * Gets the previous page number.
     * 
     * @return the previous page number or -1 if this is the first page
     */
    public int getPreviousPage() {
        return isFirstPage() ? -1 : page - 1;
    }
}
