package oop2.project.model;

import oop2.project.config.AppConfig;
import org.jetbrains.annotations.NotNull;

public class TmdbQuery {
    private String text;
    private Language language;
    private double minimumRating;
    private boolean includeAdult;
    private int page;

    public TmdbQuery() {
        AppConfig config = AppConfig.getInstance();

        this.text = "";
        this.language = Language.fromLanguageCode(config.get(AppConfig.KEY_LANGUAGE));
        this.minimumRating = config.getMinimumRating();
        this.includeAdult = config.allowAdultContent();
    }

    public TmdbQuery setText(@NotNull String text) {
        if(!text.isEmpty()){
            this.text = text;
        }

        return this;
    }

    public TmdbQuery setLanguage(@NotNull Language language) {
        this.language = language;
        return this;
    }

    public TmdbQuery setMinimumRating(double minimumRating) {
        this.minimumRating = minimumRating;
        return this;
    }

    public TmdbQuery includeAdultContent(boolean include) {
        this.includeAdult = include;
        return this;
    }

    public TmdbQuery setPage(int page) {
        this.page = page;
        return this;
    }

    public String getText() {
        return text;
    }

    public Language getLanguage() {
        return language;
    }

    public double getMinimumRating() {
        return minimumRating;
    }

    public boolean includeAdult() {
        return includeAdult;
    }

    public int getPage() {
        return page;
    }
}
