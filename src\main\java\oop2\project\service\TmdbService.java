package oop2.project.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import oop2.project.config.AppConfig;
import oop2.project.model.MovieSearchResult;
import oop2.project.model.TmdbQuery;
import org.tinylog.Logger;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * Service for interacting with The Movie Database (TMDB) API.
 */
public class TmdbService {

    private static final String BASE_URL = "https://api.themoviedb.org/3";
    private static final String DISCOVER_ENDPOINT = "/discover/movie";

    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final AppConfig config;

    public TmdbService() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.objectMapper = new ObjectMapper();
        this.config = AppConfig.getInstance();
    }

    /**
     * Discovers movies using the TMDB discover/movie endpoint.
     *
     * @param query the query parameters for movie discovery
     * @return a CompletableFuture containing the search results
     */
    public CompletableFuture<MovieSearchResult> discoverMovies(TmdbQuery query) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String url = buildDiscoverUrl(query);
                Logger.debug("Making TMDB discover request to: {}", url);

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(url))
                        .timeout(Duration.ofSeconds(30))
                        .header("Accept", "application/json")
                        .GET()
                        .build();

                HttpResponse<String> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() != 200) {
                    throw new RuntimeException("TMDB API request failed with status: " +
                            response.statusCode() + ", body: " + response.body());
                }

                return objectMapper.readValue(response.body(), MovieSearchResult.class);

            } catch (Exception e) {
                Logger.error(e, "Error discovering movies");
                throw new RuntimeException("Failed to discover movies", e);
            }
        });
    }

    private String buildDiscoverUrl(TmdbQuery query) {
        StringBuilder url = new StringBuilder(BASE_URL + DISCOVER_ENDPOINT);
        url.append("?api_key=").append(config.getTmdbApiKey());

        if (query.getLanguage() != null) {
            url.append("&language=").append(encode(query.getLanguage().getLanguageCode()));
        }

        if (query.getMinimumRating() > 0) {
            url.append("&vote_average.gte=").append(query.getMinimumRating());
        }

        url.append("&include_adult=").append(query.includeAdult());

        // Add page parameter (default to 1 if not set)
        int page = query.getPage() > 0 ? query.getPage() : 1;
        url.append("&page=").append(page);

        // Add sort by popularity descending for better results
        url.append("&sort_by=popularity.desc");

        return url.toString();
    }

    private String encode(String value) {
        return URLEncoder.encode(value, StandardCharsets.UTF_8);
    }
}
