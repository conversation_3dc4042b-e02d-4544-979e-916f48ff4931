package oop2.project.config;

import oop2.project.model.Language;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for AppConfig language handling.
 */
public class AppConfigTest {

    @Test
    public void testLanguageHandling() {
        // Given
        AppConfig config = AppConfig.getInstance();
        
        // When setting a language
        config.setLanguage(Language.English);
        
        // Then it should be retrievable correctly
        Language retrievedLanguage = config.getLanguage();
        assertEquals(Language.English, retrievedLanguage);
        assertEquals("en-US", retrievedLanguage.getLanguageCode());
    }

    @Test
    public void testLanguageHandlingWithGerman() {
        // Given
        AppConfig config = AppConfig.getInstance();
        
        // When setting German language
        config.setLanguage(Language.Deutsch);
        
        // Then it should be retrievable correctly
        Language retrievedLanguage = config.getLanguage();
        assertEquals(Language.Deutsch, retrievedLanguage);
        assertEquals("de-CH", retrievedLanguage.getLanguageCode());
    }

    @Test
    public void testDefaultLanguageHandling() {
        // Given - a fresh config instance should have default language
        AppConfig config = AppConfig.getInstance();
        
        // When getting the default language
        Language defaultLanguage = config.getLanguage();
        
        // Then it should be English (default)
        assertNotNull(defaultLanguage);
        assertEquals("en-US", defaultLanguage.getLanguageCode());
    }

    @Test
    public void testMinimumRatingHandling() {
        // Given
        AppConfig config = AppConfig.getInstance();
        
        // When setting minimum rating
        config.setMinimumRating(7.5);
        
        // Then it should be retrievable correctly
        double retrievedRating = config.getMinimumRating();
        assertEquals(7.5, retrievedRating, 0.01);
    }

    @Test
    public void testAdultContentHandling() {
        // Given
        AppConfig config = AppConfig.getInstance();
        
        // When setting adult content preference
        config.setAllowAdultContent(true);
        
        // Then it should be retrievable correctly
        assertTrue(config.allowAdultContent());
        
        // When setting to false
        config.setAllowAdultContent(false);
        
        // Then it should be false
        assertFalse(config.allowAdultContent());
    }
}
